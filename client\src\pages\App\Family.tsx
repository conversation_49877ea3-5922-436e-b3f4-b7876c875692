import React from 'react';
import {
  Typography,
  Card,
  CardContent,
} from '@mui/material';
import { AccountCircle } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import AppPage from '../../components/App/AppPage';
import { DashboardProps } from '../../types';
import {
  PaddedContent,
  ExtraLargeIconTypography,
  SpacedTypography,
  ResponsiveGridContainer,
  CenteredAvatar,
} from '../../components/styled';

const Family: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();

  const familyMembers = [
    { name: '<PERSON> (Age 8)', school: 'Lincoln Elementary - 3rd Grade' },
    { name: '<PERSON> (Age 12)', school: 'Roosevelt Middle School - 7th Grade' }
  ];

  return (
    <AppPage user={user} onLogout={onLogout} title="Family">
      <PaddedContent textAlign="center">
        <Card>
          <CardContent>
            <ExtraLargeIconTypography variant="h2">👨👩👧👦</ExtraLargeIconTypography>
            <Typography variant="h5" gutterBottom>Family Management</Typography>
            <SpacedTypography variant="body1" color="text.secondary">
              Manage family members, children profiles, and sharing settings.
            </SpacedTypography>
            <ResponsiveGridContainer>
              {familyMembers.map((child, index) => (
                <Card key={index} variant="outlined">
                  <CardContent>
                    <CenteredAvatar>
                      <AccountCircle />
                    </CenteredAvatar>
                    <Typography variant="h6">{child.name}</Typography>
                    <Typography variant="body2" color="text.secondary">{child.school}</Typography>
                  </CardContent>
                </Card>
              ))}
            </ResponsiveGridContainer>
          </CardContent>
        </Card>
      </PaddedContent>
    </AppPage>
  );
};

export default Family;

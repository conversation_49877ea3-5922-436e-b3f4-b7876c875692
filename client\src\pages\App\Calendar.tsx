import React from 'react';
import {
  Typography,
  Card,
  CardContent,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import AppPage from '../../components/App/AppPage';
import { DashboardProps } from '../../types';
import {
  PaddedContent,
  ExtraLargeIconTypography,
} from '../../components/styled';

const Calendar: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();

  return (
    <AppPage user={user} onLogout={onLogout} title="Calendar">
      <PaddedContent textAlign="center">
        <Card>
          <CardContent>
            <ExtraLargeIconTypography variant="h2">📅</ExtraLargeIconTypography>
            <Typography variant="h5" gutterBottom>Calendar View</Typography>
            <Typography variant="body1" color="text.secondary">
              Calendar integration coming soon. View all your family's events and deadlines in one place.
            </Typography>
          </CardContent>
        </Card>
      </PaddedContent>
    </AppPage>
  );
};

export default Calendar;

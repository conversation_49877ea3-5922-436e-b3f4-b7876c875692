import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery,
  CssBaseline,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  Search,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import NavigationDrawer from '../NavigationDrawer/NavigationDrawer';
import { User } from '../../../types';


const DRAWER_WIDTH = 280;
const MINI_DRAWER_WIDTH = 64;

interface AppPageProps {
  children: React.ReactNode;
  user: User | null;
  onLogout: () => void;
  title?: string;
  showAppBar?: boolean;
}

// Styled Components
const Main = styled('main', {
  shouldForwardProp: (prop) => !['open', 'mini'].includes(prop as string)
})<{ open?: boolean; mini?: boolean }>(({ theme, open, mini }) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: 0,
  [theme.breakpoints.up('md')]: {
    marginLeft: open ? (mini ? MINI_DRAWER_WIDTH : DRAWER_WIDTH) : 0,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}));

const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('md')]: {
    width: open ? `calc(100% - ${DRAWER_WIDTH}px)` : '100%',
    marginLeft: open ? DRAWER_WIDTH : 0,
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  minHeight: 'calc(100vh - 64px)', // Account for app bar height
  backgroundColor: theme.palette.background.default,
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

// Additional styled components for AppPage
const FlexBox = styled(Box)(() => ({
  display: 'flex',
}));

const MenuIconButton = styled(IconButton, {
  shouldForwardProp: (prop) => !['hideWhenOpen', 'drawerOpen', 'isMobile'].includes(prop as string),
})<{ hideWhenOpen?: boolean; drawerOpen?: boolean; isMobile?: boolean }>(({ theme, hideWhenOpen, drawerOpen, isMobile }) => ({
  marginRight: theme.spacing(2),
  ...(hideWhenOpen && drawerOpen && !isMobile && {
    display: 'none',
  }),
}));

const AppTitleTypography = styled(Typography)(() => ({
  flexGrow: 1,
  fontWeight: 600,
}));

const AppBarActions = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: 8, // 1 spacing unit
}));

const AppPage: React.FC<AppPageProps> = ({
  children,
  user,
  onLogout,
  title,
  showAppBar = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Initialize drawer state from localStorage or default values
  const [drawerOpen, setDrawerOpen] = useState(() => {
    if (isMobile) return false; // Always closed on mobile initially
    const saved = localStorage.getItem('drawerOpen');
    return saved !== null ? JSON.parse(saved) : true; // Default to open on desktop
  });

  const [miniDrawer, setMiniDrawer] = useState(() => {
    const saved = localStorage.getItem('miniDrawer');
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Persist drawer state changes to localStorage
  useEffect(() => {
    if (!isMobile) { // Only persist on desktop
      localStorage.setItem('drawerOpen', JSON.stringify(drawerOpen));
    }
  }, [drawerOpen, isMobile]);

  useEffect(() => {
    localStorage.setItem('miniDrawer', JSON.stringify(miniDrawer));
  }, [miniDrawer]);

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleDrawerClose = () => {
    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const handleToggleMini = () => {
    setMiniDrawer(!miniDrawer);
  };

  // Reset drawer state on mobile/desktop transition
  useEffect(() => {
    if (isMobile && drawerOpen) {
      setDrawerOpen(false);
    } else if (!isMobile && !drawerOpen) {
      // Restore from localStorage or default to open on desktop
      const saved = localStorage.getItem('drawerOpen');
      const shouldOpen = saved !== null ? JSON.parse(saved) : true;
      setDrawerOpen(shouldOpen);
    }
  }, [isMobile]); // Only depend on isMobile to avoid infinite loops

  return (
    <FlexBox>
      <CssBaseline />

      {/* App Bar */}
      {showAppBar && (
        <StyledAppBar
          position="fixed"
          open={drawerOpen && !isMobile}
          elevation={1}
          style={{
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Toolbar>
            <MenuIconButton
              color="inherit"
              aria-label="toggle drawer"
              onClick={handleDrawerToggle}
              edge="start"
              hideWhenOpen
              drawerOpen={drawerOpen}
              isMobile={isMobile}
            >
              <MenuIcon />
            </MenuIconButton>

            <AppTitleTypography
              variant="h6"
              noWrap
            >
              {title || 'Dashboard'}
            </AppTitleTypography>

            {/* App Bar Actions */}
            <AppBarActions>
              <IconButton color="inherit" size="large">
                <Search />
              </IconButton>
              <IconButton color="inherit" size="large">
                <Notifications />
              </IconButton>
            </AppBarActions>
          </Toolbar>
        </StyledAppBar>
      )}

      {/* Navigation Drawer */}
      <NavigationDrawer
        open={drawerOpen}
        onClose={handleDrawerClose}
        onOpen={handleDrawerOpen}
        user={user}
        onLogout={onLogout}
        variant={isMobile ? 'temporary' : 'persistent'}
        mini={!isMobile && miniDrawer}
        onToggleMini={handleToggleMini}
      />

      {/* Main Content */}
      <Main open={drawerOpen && !isMobile} mini={miniDrawer}>
        {showAppBar && <Toolbar />} {/* Spacer for fixed app bar */}
        <ContentContainer>
          {children}
        </ContentContainer>
      </Main>
    </FlexBox>
  );
};

export default AppPage;

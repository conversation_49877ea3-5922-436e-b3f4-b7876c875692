import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Grid,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import AppPage from '../../components/App/AppPage';
import { DashboardProps, Activity, Reminder } from '../../types';
import {
  PaddedContent,
  StatsGridContainer,
  CenteredCardContent,
  LargeIconTypography,
  SpacedCard,
  FlexContainer,
} from '../../components/styled';

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();
  // Mock data for overview stats - in a real app, this would come from an API
  const messageCount = 3; // This could be fetched from an API or passed as props

  const mockActivities: Activity[] = [
    {
      id: 1,
      title: 'Picture Day - Lincoln Elementary',
      category: 'school_event',
      priority: 'medium',
      date: '2024-02-15',
      child: 'Emma',
      status: 'upcoming',
      description: 'School picture day. Remember to dress <PERSON> in her blue dress.'
    },
    {
      id: 2,
      title: 'Soccer Practice Cancelled',
      category: 'sports',
      priority: 'high',
      date: '2024-02-12',
      child: 'Jake',
      status: 'urgent',
      description: 'Due to weather conditions, soccer practice is cancelled today.'
    }
  ];

  const mockReminders: Reminder[] = [
    { id: 1, text: 'Permission slip for field trip due tomorrow', priority: 'high' },
    { id: 2, text: 'Bring snacks for Emma\'s class party on Friday', priority: 'medium' }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      default: return 'success';
    }
  };

  return (
    <AppPage user={user} onLogout={onLogout} title="Dashboard">
      <PaddedContent>
        <StatsGridContainer container spacing={3}>
          {[
            { icon: '📧', number: messageCount.toString(), label: t('dashboard.newMessages') },
            { icon: '⏰', number: '5', label: t('dashboard.upcomingEvents') },
            { icon: '🚨', number: '2', label: t('dashboard.urgentItems') },
            { icon: '👨👩👧👦', number: '2', label: t('dashboard.children') }
          ].map((stat, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
              <Card>
                <CenteredCardContent>
                  <LargeIconTypography variant="h2">{stat.icon}</LargeIconTypography>
                  <Typography variant="h4" component="div">{stat.number}</Typography>
                  <Typography variant="body2" color="text.secondary">{stat.label}</Typography>
                </CenteredCardContent>
              </Card>
            </Grid>
          ))}
        </StatsGridContainer>

        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 8 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{t('dashboard.recentActivities')}</Typography>
                {mockActivities.map(activity => (
                  <SpacedCard key={activity.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <FlexContainer justify="space-between" align="center" gap={1}>
                          <Typography variant="h6">{activity.title}</Typography>
                          <Chip
                            label={activity.priority}
                            color={getPriorityColor(activity.priority) as any}
                            size="small"
                          />
                        </FlexContainer>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          👤 {activity.child} • 📅 {activity.date}
                        </Typography>
                        <Typography variant="body2">{activity.description}</Typography>
                      </CardContent>
                    </Card>
                  </SpacedCard>
                ))}
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{t('dashboard.quickReminders')}</Typography>
                <List>
                  {mockReminders.map(reminder => (
                    <ListItem key={reminder.id} divider>
                      <ListItemText primary={reminder.text} />
                      <ListItemSecondaryAction>
                        <Button size="small" variant="outlined">{t('dashboard.done')}</Button>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </PaddedContent>
    </AppPage>
  );
};

export default Dashboard;
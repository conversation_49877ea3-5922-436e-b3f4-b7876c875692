import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Box,
  IconButton
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Delete, Refresh } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import AppPage from '../../components/App/AppPage';
import { DashboardProps, Message } from '../../types';
import {
  PaddedContent,
  FlexContainer,
} from '../../components/styled';

const Messages: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');

  const fetchMessages = async (): Promise<void> => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/messages', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data: Message[] = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setMessages([
        {
          id: '1',
          from: 'Lincoln Elementary <<EMAIL>>',
          subject: 'Picture Day Reminder - February 15th',
          snippet: 'Don\'t forget! Picture day is coming up on February 15th...',
          date: '2024-02-10'
        }
      ]);
    }
    setLoading(false);
  };

  const deleteMessage = async (messageId: string): Promise<void> => {
    try {
      const response = await fetch(`/api/email/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        setMessages(messages.filter(m => m.id !== messageId));
      }
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };

  useEffect(() => {
    fetchMessages();
  }, []);

  // Periodic refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchMessages, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <AppPage user={user} onLogout={onLogout} title="Messages">
      <PaddedContent>
        <FlexContainer justify="space-between" align="center" gap={3}>
          <Typography variant="h5">{t('dashboard.allMessages')}</Typography>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={fetchMessages}
            disabled={loading}
          >
            {loading ? t('dashboard.loading') : t('dashboard.refresh')}
          </Button>
        </FlexContainer>

        <TextField
          label="Search messages"
          variant="outlined"
          size="small"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ marginTop: '16px', marginBottom: '8px', width: '300px' }}
        />
        
        <div style={{ height: 600, width: '100%', marginTop: '8px' }}>
          <DataGrid
            rows={messages
              .filter(msg => 
                !searchText || 
                msg.from.toLowerCase().includes(searchText.toLowerCase()) ||
                msg.subject.toLowerCase().includes(searchText.toLowerCase()) ||
                msg.snippet.toLowerCase().includes(searchText.toLowerCase())
              )
              .map(msg => ({
                id: msg.id,
                from: msg.from,
                subject: msg.subject,
                summary: msg.snippet,
                rawJson: msg.body || 'N/A',
                date: msg.date,
                dateFormatted: new Date(msg.date).toLocaleDateString()
              }))}
            columns={[
              { field: 'from', headerName: t('dashboard.from'), width: 200 },
              { field: 'subject', headerName: t('dashboard.subject'), width: 250 },
              { 
                field: 'summary', 
                headerName: t('dashboard.summary'), 
                width: 300,
                renderCell: (params) => (
                  <Box sx={{ 
                    whiteSpace: 'pre-wrap',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'flex-start',
                    py: 1
                  }}>
                    {params.value}
                  </Box>
                )
              },
              { 
                field: 'rawJson', 
                headerName: t('dashboard.rawJson'), 
                width: 400, 
                sortable: false,
                renderCell: (params) => (
                  <Box sx={{ 
                    fontSize: '0.75rem', 
                    fontFamily: 'monospace', 
                    whiteSpace: 'pre-wrap',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'flex-start',
                    py: 1,
                    overflow: 'auto'
                  }}>
                    {params.value}
                  </Box>
                )
              },
              { field: 'dateFormatted', headerName: t('dashboard.date'), width: 120 },
              {
                field: 'actions',
                headerName: '',
                width: 80,
                sortable: false,
                renderCell: (params) => (
                  <IconButton
                    size="small"
                    onClick={() => deleteMessage(params.row.id)}
                    color="error"
                  >
                    <Delete />
                  </IconButton>
                )
              }
            ] as GridColDef[]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } },
              sorting: { sortModel: [{ field: 'date', sort: 'desc' }] }
            }}
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
            getRowHeight={() => 'auto'}
          />
        </div>
      </PaddedContent>
    </AppPage>
  );
};

export default Messages;
